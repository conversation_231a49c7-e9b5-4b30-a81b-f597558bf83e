// TK8620 射频通信协议帧解码器
// 参考规范: aiTalk SDD §4.1.3.1 & §4.1.3.2
// 该文件实现了TK8620协议帧的解码和载荷解析功能

import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../utils/encryption_utils.dart';
import 'tk8620_protocol.dart';

/// TK8620协议帧解码器
/// 负责将字节序列解码为TK8620Frame对象
class TK8620FrameDecoder {
  TK8620FrameDecoder._();

  /// 从字节序列解码帧
  static TK8620Frame? decode(Uint8List bytes) {
    if (bytes.isEmpty) return null;

    try {
      int offset = 0;

      // 1. 解析帧控制字段
      final frameCtrlByte = bytes[offset++];
      final version =
          (frameCtrlByte & TK8620FrameCtrl.versionMask) >>
          TK8620FrameCtrl.versionShift;
      final frameType = TK8620FrameType.fromInt(
        (frameCtrlByte & TK8620FrameCtrl.frameTypeMask) >>
            TK8620FrameCtrl.frameTypeShift,
      );
      final communicationMode = TK8620CommunicationMode.fromInt(
        (frameCtrlByte & TK8620FrameCtrl.commModeMask) >>
            TK8620FrameCtrl.commModeShift,
      );

      int frameCnt = 0;
      int srcId = 0;
      int dstId = 0;
      int subPkgNum = 1;
      int subPkgNo = 0;
      bool useLongSrcId = false;

      // 2. 根据帧类型解析不同的头部
      if (frameType == TK8620FrameType.voice) {
        // 语音帧简化头部: SrcID(1) + SubPkgNo(1)
        if (bytes.length < 3) return null;
        srcId = bytes[offset++];
        subPkgNo = bytes[offset++];
      } else {
        // 非语音帧完整头部
        // Session 帧支持 4 字节 SrcID
        if (frameType == TK8620FrameType.session) {
          if (bytes.length < 13)
            return null; // FrameCtrl(1)+FrameCnt(2)+SrcID(4)+DstID(4)+SubPkgNum(1)+SubPkgNo(1)
        } else {
          if (bytes.length < 10)
            return null; // FrameCtrl(1)+FrameCnt(2)+SrcID(1)+DstID(4)+SubPkgNum(1)+SubPkgNo(1)
        }

        // FrameCnt(2) - 大端序
        frameCnt = (bytes[offset] << 8) | bytes[offset + 1];
        offset += 2;

        if (frameType == TK8620FrameType.session) {
          // 4 字节 SrcID (大端序)
          useLongSrcId = true;
          srcId =
              (bytes[offset] << 24) |
              (bytes[offset + 1] << 16) |
              (bytes[offset + 2] << 8) |
              bytes[offset + 3];
          offset += 4;
        } else {
          // 1 字节 SrcID
          srcId = bytes[offset++];
        }

        // DstID(4) - 大端序
        dstId =
            (bytes[offset] << 24) |
            (bytes[offset + 1] << 16) |
            (bytes[offset + 2] << 8) |
            bytes[offset + 3];
        offset += 4;

        // SubPkgNum(1)
        subPkgNum = bytes[offset++];

        // SubPkgNo(1)
        subPkgNo = bytes[offset++];
      }

      // 3. 提取有效载荷
      final payload = Uint8List.fromList(bytes.sublist(offset));

      return TK8620Frame(
        frameType: frameType,
        communicationMode: communicationMode,
        version: version,
        frameCnt: frameCnt,
        srcId: srcId,
        dstId: dstId,
        subPkgNum: subPkgNum,
        subPkgNo: subPkgNo,
        payload: payload,
        useLongSrcId: useLongSrcId,
      );
    } catch (e) {
      debugPrint('TK8620Frame解码错误: $e');
      return null;
    }
  }

  /// 尝试解码帧，如果失败返回null
  static TK8620Frame? tryDecode(Uint8List bytes) {
    return decode(bytes);
  }

  /// 验证帧数据的完整性
  static bool validateFrame(Uint8List bytes) {
    if (bytes.isEmpty) return false;

    try {
      // 解析帧控制字段
      final frameCtrlByte = bytes[0];
      final frameTypeValue =
          (frameCtrlByte & TK8620FrameCtrl.frameTypeMask) >>
          TK8620FrameCtrl.frameTypeShift;
      final frameType = TK8620FrameType.fromInt(frameTypeValue);

      // 根据帧类型验证最小长度
      if (frameType == TK8620FrameType.voice) {
        return bytes.length >= 3; // 语音帧最少3字节
      } else if (frameType == TK8620FrameType.session) {
        return bytes.length >= 13; // 会话帧最少13字节 (含4字节SrcID)
      } else {
        return bytes.length >= 10; // 其他非语音帧最少10字节
      }
    } catch (e) {
      return false;
    }
  }
}

/// TK8620协议载荷解析器
/// 参考规范: aiTalk SDD §4.1.3.3 帧载荷定义 (FPayload)
class TK8620PayloadParser {
  TK8620PayloadParser._();

  /// 解析会话请求载荷
  static TK8620SessionJoinRequest? parseJoinRequest(Uint8List payload) {
    if (payload.length < 37) return null; // 控制码(1) + Password(4) + UserName(32)

    try {
      int offset = 0;

      // 验证控制码
      final controlCode = payload[offset++];
      if (controlCode != TK8620SessionCode.joinRequest) return null;

      // SessionPassword (4字节)
      final sessionPassword =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];
      offset += 4;

      // UserName (32字节，UTF-8编码)
      final nameBytes = payload.sublist(offset, offset + 32);
      final userName = utf8
          .decode(nameBytes, allowMalformed: true)
          .replaceAll('\x00', '')
          .trim();

      // 调试输出
      debugPrint('🔑 解析会话入网请求 -> user: $userName, pwd: $sessionPassword');

      return TK8620SessionJoinRequest(
        sessionPassword: sessionPassword,
        userName: userName,
      );
    } catch (e) {
      debugPrint('解析会话请求载荷错误: $e');
      return null;
    }
  }

  /// 解析会话响应载荷
  static TK8620SessionJoinResponse? parseJoinResponse(Uint8List payload) {
    // 最小长度: 控制码(1) + 响应码(1) + 成员ID(1) + SessionID(4) + 群名(24)
    if (payload.length < 31) return null;

    try {
      int offset = 0;

      // 1. 控制码
      final controlCode = payload[offset++];
      if (controlCode != TK8620SessionCode.joinResponse) return null;

      // 2. 响应码
      final responseCode = payload[offset++];

      // 3. 群内成员ID (1 字节)
      final memberId = payload[offset++];

      // 4. SessionID (4 字节)
      final sessionId =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];
      offset += 4;

      // 5. 群名称 (24 字节，UTF-8，可能含空)
      final nameBytes = payload.sublist(offset, offset + 24);
      final groupName = utf8
          .decode(nameBytes, allowMalformed: true)
          .replaceAll('\x00', '')
          .trim();
      offset += 24;

      // 6. 成员列表 (可选，5字节/成员)
      final Map<int, int> members = {};
      while (offset + 5 <= payload.length) {
        final mId = payload[offset++];
        final did =
            (payload[offset] << 24) |
            (payload[offset + 1] << 16) |
            (payload[offset + 2] << 8) |
            payload[offset + 3];
        offset += 4;
        members[mId] = did;
      }

      debugPrint(
        '📝 解析会话入网响应 -> code:$responseCode, memberId:$memberId, sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}, groupName:$groupName, members:${members.length}',
      );

      return TK8620SessionJoinResponse(
        responseCode: responseCode,
        memberId: memberId,
        sessionId: sessionId,
        groupName: groupName,
        members: members,
      );
    } catch (e) {
      debugPrint('解析会话响应载荷错误: $e');
      return null;
    }
  }

  /// 解析文本数据载荷
  static Future<TK8620TextData?> parseTextData(
    Uint8List payload, {
    String? groupId,
  }) async {
    if (payload.length < 2) return null; // 至少需要类型码(1) + 数据(1+)

    try {
      int offset = 0;

      // 验证数据类型码
      final dataType = payload[offset++];
      if (dataType != TK8620DataType.text) return null;

      // 文本数据 (可能已加密)
      final textBytes = payload.sublist(offset);

      // 调试群组ID信息
      debugPrint('🆔 [parseTextData] 群组ID: "$groupId"');
      debugPrint(
        '📦 [parseTextData] 文本字节: ${textBytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join(' ')}',
      );

      // 解密文本数据（如果有群组ID）
      String text;
      if (groupId != null && groupId.isNotEmpty) {
        debugPrint('🔓 [parseTextData] 开始解密...');
        text = await EncryptionUtils.decryptTextMessage(groupId, textBytes);
      } else {
        debugPrint('⚠️ [parseTextData] 群组ID为空，使用UTF-8直接解码');
        text = utf8.decode(textBytes, allowMalformed: true);
      }

      // 调试输出
      debugPrint('💬 文本数据: $text');

      return TK8620TextData(text: text);
    } catch (e) {
      debugPrint('解析文本数据载荷错误: $e');
      return null;
    }
  }

  /// 解析GPS数据载荷
  static TK8620GPSData? parseGPSData(Uint8List payload) {
    if (payload.length < 13) return null; // 类型码(1) + 纬度(4) + 经度(4) + 海拔(4)

    try {
      int offset = 0;

      // 验证数据类型码
      final dataType = payload[offset++];
      if (dataType != TK8620DataType.gps) return null;

      // 纬度 (4字节)
      final latInt =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];
      offset += 4;

      // 经度 (4字节)
      final longInt =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];
      offset += 4;

      // 海拔 (4字节)
      final altInt =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];

      // 转换为浮点数
      final latitude = latInt / 10000000.0;
      final longitude = longInt / 10000000.0;
      final altitude = altInt / 100.0;

      // 调试输出
      debugPrint('📍 GPS 数据: lat=$latitude, lng=$longitude, alt=$altitude');

      return TK8620GPSData(
        latitude: latitude,
        longitude: longitude,
        altitude: altitude,
      );
    } catch (e) {
      debugPrint('解析GPS数据载荷错误: $e');
      return null;
    }
  }

  /// 解析语音数据载荷
  static Future<TK8620VoiceData?> parseVoiceData(
    Uint8List payload, {
    String? groupId,
  }) async {
    if (payload.isEmpty) return null;

    try {
      int offset = 0;

      // 填充字节信息
      final paddingByte = payload[offset++];
      final isLastPacket = (paddingByte & 0x80) != 0;

      // 音频数据（可能已加密）
      final encryptedAudioData = payload.sublist(offset);

      // 解密音频数据（如果有群组ID）
      Uint8List audioData;
      if (groupId != null && groupId.isNotEmpty) {
        audioData = await EncryptionUtils.decryptVoiceData(
          groupId,
          encryptedAudioData,
        );
      } else {
        audioData = encryptedAudioData;
      }

      return TK8620VoiceData(audioData: audioData, isLastPacket: isLastPacket);
    } catch (e) {
      debugPrint('解析语音数据载荷错误: $e');
      return null;
    }
  }

  /// 解析实时通话语音数据载荷
  static TK8620RealTimeVoiceData? parseRealTimeVoiceData(Uint8List payload) {
    if (payload.length < 2) return null; // 至少需要数据类型(1) + 填充字节(1)

    try {
      int offset = 0;

      // 验证数据类型
      final dataType = payload[offset++];
      if (dataType != TK8620DataType.realTimeVoice) return null;

      // 填充字节信息
      final paddingByte = payload[offset++];
      final isLastPacket = (paddingByte & 0x80) != 0;

      // 音频数据
      final audioData = payload.sublist(offset);

      return TK8620RealTimeVoiceData(
        audioData: audioData,
        isLastPacket: isLastPacket,
      );
    } catch (e) {
      debugPrint('解析实时通话语音数据载荷错误: $e');
      return null;
    }
  }

  /// 解析建立通话请求载荷
  static TK8620CreateTalkRequest? parseCreateTalkRequest(Uint8List payload) {
    if (payload.length < 5) return null; // 控制码(1) + SessionID(4)

    try {
      int offset = 0;

      // 验证控制码
      final controlCode = payload[offset++];
      if (controlCode != TK8620SessionCode.createTalkRequest) return null;

      // SessionID (4字节)
      final sessionId =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];

      debugPrint(
        '📞 解析建立通话请求 -> sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      return TK8620CreateTalkRequest(sessionId: sessionId);
    } catch (e) {
      debugPrint('解析建立通话请求载荷错误: $e');
      return null;
    }
  }

  /// 解析建立通话响应载荷
  static TK8620CreateTalkResponse? parseCreateTalkResponse(Uint8List payload) {
    if (payload.length < 6) return null; // 控制码(1) + 响应码(1) + SessionID(4)

    try {
      int offset = 0;

      // 验证控制码
      final controlCode = payload[offset++];
      if (controlCode != TK8620SessionCode.createTalkResponse) return null;

      // 响应码 (1字节)
      final responseCode = payload[offset++];

      // SessionID (4字节)
      final sessionId =
          (payload[offset] << 24) |
          (payload[offset + 1] << 16) |
          (payload[offset + 2] << 8) |
          payload[offset + 3];

      debugPrint(
        '📞 解析建立通话响应 -> code:$responseCode, sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      return TK8620CreateTalkResponse(
        responseCode: responseCode,
        sessionId: sessionId,
      );
    } catch (e) {
      debugPrint('解析建立通话响应载荷错误: $e');
      return null;
    }
  }

  /// 解析加入会话通知载荷
  static TK8620JoinNotification? parseJoinNotification(
    Uint8List payload,
    int srcId,
    int dstId,
  ) {
    if (payload.length < 2) return null; // 控制码(1) + MemberId(1)

    try {
      int offset = 0;

      // 验证控制码
      final controlCode = payload[offset++];
      if (controlCode != TK8620SessionCode.joinNotify) return null;

      // MemberId (1字节)
      final memberId = payload[offset++];

      // DeviceID从帧头SrcID获取，SessionID从帧头DstID获取
      final deviceId = srcId;
      final sessionId = dstId;

      debugPrint(
        '👥 解析加入会话通知 -> memberId:$memberId, deviceId:0x${deviceId.toRadixString(16).padLeft(8, '0')}, sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}',
      );

      return TK8620JoinNotification(
        memberId: memberId,
        deviceId: deviceId,
        sessionId: sessionId,
      );
    } catch (e) {
      debugPrint('解析加入会话通知载荷错误: $e');
      return null;
    }
  }
}

/// 会话加入请求数据结构
class TK8620SessionJoinRequest {
  const TK8620SessionJoinRequest({
    required this.sessionPassword,
    required this.userName,
  });

  final int sessionPassword;
  final String userName;

  @override
  String toString() {
    return 'TK8620SessionJoinRequest(userName: $userName)';
  }
}

/// 会话加入响应数据结构
class TK8620SessionJoinResponse {
  const TK8620SessionJoinResponse({
    required this.responseCode,
    required this.memberId,
    required this.sessionId,
    required this.groupName,
    required this.members,
  });

  final int responseCode;
  final int memberId;
  final int sessionId;
  final String groupName;
  final Map<int, int> members; // key: memberId, value: deviceId

  bool get isSuccess => responseCode == 0;

  @override
  String toString() {
    return 'TK8620SessionJoinResponse(code:$responseCode, memberId:$memberId, sessionId:0x${sessionId.toRadixString(16).padLeft(8, '0')}, groupName:$groupName, members:${members.length})';
  }
}

/// 文本数据结构
class TK8620TextData {
  const TK8620TextData({required this.text});

  final String text;

  @override
  String toString() {
    return 'TK8620TextData(text: $text)';
  }
}

/// GPS数据结构
class TK8620GPSData {
  const TK8620GPSData({
    required this.latitude,
    required this.longitude,
    required this.altitude,
  });

  final double latitude;
  final double longitude;
  final double altitude;

  @override
  String toString() {
    return 'TK8620GPSData(lat: $latitude, lng: $longitude, alt: $altitude)';
  }
}

/// 语音数据结构
class TK8620VoiceData {
  const TK8620VoiceData({required this.audioData, required this.isLastPacket});

  final Uint8List audioData;
  final bool isLastPacket;

  @override
  String toString() {
    return 'TK8620VoiceData(length: ${audioData.length}, isLast: $isLastPacket)';
  }
}

/// 实时通话语音数据结构
class TK8620RealTimeVoiceData {
  const TK8620RealTimeVoiceData({
    required this.audioData,
    required this.isLastPacket,
  });

  final Uint8List audioData;
  final bool isLastPacket;

  @override
  String toString() {
    return 'TK8620RealTimeVoiceData(length: ${audioData.length}, isLast: $isLastPacket)';
  }
}

/// 建立通话请求数据结构
class TK8620CreateTalkRequest {
  const TK8620CreateTalkRequest({required this.sessionId});

  final int sessionId;

  @override
  String toString() {
    return 'TK8620CreateTalkRequest(sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')})';
  }
}

/// 建立通话响应数据结构
class TK8620CreateTalkResponse {
  const TK8620CreateTalkResponse({
    required this.responseCode,
    required this.sessionId,
  });

  final int responseCode;
  final int sessionId;

  bool get isSuccess => responseCode == 0;

  @override
  String toString() {
    return 'TK8620CreateTalkResponse(code: $responseCode, sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')})';
  }
}

/// 加入会话通知数据结构
class TK8620JoinNotification {
  const TK8620JoinNotification({
    required this.memberId,
    required this.deviceId,
    required this.sessionId,
  });

  /// 新加入成员的群内成员ID
  final int memberId;

  /// 新加入成员的设备ID (从帧头SrcID获取)
  final int deviceId;

  /// 群组会话ID (从帧头DstID获取)
  final int sessionId;

  @override
  String toString() {
    return 'TK8620JoinNotification(memberId: $memberId, deviceId: 0x${deviceId.toRadixString(16).padLeft(8, '0')}, sessionId: 0x${sessionId.toRadixString(16).padLeft(8, '0')})';
  }
}
