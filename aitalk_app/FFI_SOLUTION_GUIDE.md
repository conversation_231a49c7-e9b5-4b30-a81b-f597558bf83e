# MELP iOS FFI解决方案指南

## 问题分析
错误 `Failed to lookup symbol 'melp_init': symbol not found` 的根本原因是：
- MELP函数没有被编译到iOS主程序中
- `DynamicLibrary.process()` 无法找到这些符号

## 解决方案：在Xcode中直接添加MELP源文件

### 第1步：在Xcode中添加MELP源文件

#### 1.1 打开项目
```bash
cd ios
open Runner.xcworkspace
```

#### 1.2 添加源文件到项目
1. 右键点击左侧的 **Runner** 项目
2. 选择 **Add Files to "Runner"**
3. 导航到 `../shared/melp/MELPe_fxp/` 目录
4. 选择以下文件（**重要：只选择这些文件**）：

**必须添加的.c文件：**
```
✅ melp_ana.c
✅ melp_syn.c  
✅ melp_chn.c
✅ dsp_sub.c
✅ mat_lib.c
✅ pit_lib.c
✅ qnt_lib.c
✅ fs_lib.c
✅ vq_lib.c
✅ Win32/mathhalf.c
```

**不要添加这些文件（会冲突）：**
```
❌ melp_enc.c
❌ melp_dec.c  
❌ sc12enc.c
❌ sc24enc.c
❌ sc12dec.c
❌ sc24dec.c
❌ sc1200.c
```

#### 1.3 添加包装器文件
同时添加我们的包装器文件：
```
✅ Runner/MelpCodec/melp_ios.c
✅ Runner/MelpCodec/melp_ios.h
```

#### 1.4 确保添加选项正确
- ✅ 勾选 **"Copy items if needed"**
- ✅ 选择 **"Create groups"**
- ✅ 确保 **Runner** target被选中

### 第2步：配置Build Settings

#### 2.1 Header Search Paths
在Build Settings中搜索 `Header Search Paths`，添加：
```
$(SRCROOT)/../shared/melp/MELPe_fxp
$(SRCROOT)/Runner/MelpCodec
```

#### 2.2 Other C Flags  
在Build Settings中搜索 `Other C Flags`，添加：
```
-U__arm__
```

### 第3步：验证配置

#### 3.1 检查文件是否正确添加
在Xcode左侧项目导航器中，应该看到：
```
Runner/
├── melp_ana.c
├── melp_syn.c
├── melp_chn.c
├── dsp_sub.c
├── mat_lib.c
├── pit_lib.c
├── qnt_lib.c
├── fs_lib.c
├── vq_lib.c
├── mathhalf.c
├── melp_ios.c
└── melp_ios.h
```

#### 3.2 编译测试
按 **Cmd+B** 编译项目，确保没有编译错误。

### 第4步：测试FFI

#### 4.1 运行应用
```bash
flutter run -d ios
```

#### 4.2 预期结果
- ✅ 应用启动无错误
- ✅ 日志显示：`MELP initialized using FFI on ios`
- ✅ 不再出现 "symbol not found" 错误

### 第5步：如果仍有问题

#### 5.1 清理项目
在Xcode中：Product → Clean Build Folder

#### 5.2 检查符号导出
确保 `melp_ios.h` 中有正确的导出声明：
```c
#if defined(__GNUC__) && __GNUC__ >= 4
    #define MELP_EXPORT __attribute__((visibility("default")))
#else
    #define MELP_EXPORT
#endif

MELP_EXPORT int melp_init(int rate_bps);
MELP_EXPORT int melp_set_rate(int rate_bps);
MELP_EXPORT int melp_encode_frame(const int16_t *pcm, uint8_t *bits);
MELP_EXPORT int melp_decode_frame(const uint8_t *bits, int16_t *pcm);
```

#### 5.3 验证链接
在终端中检查编译后的二进制文件：
```bash
nm ios/build/Release-iphoneos/Runner.app/Runner | grep melp
```
应该看到melp相关的符号。

## 为什么这样做有效？

1. **直接编译**：MELP源文件直接编译到主程序中
2. **符号可见**：使用 `MELP_EXPORT` 确保符号对FFI可见
3. **避免冲突**：只包含必要的源文件，避免重复定义
4. **正确配置**：Header Search Paths确保头文件能被找到

## 技术原理

### FFI符号查找过程：
1. `DynamicLibrary.process()` 在主程序符号表中查找
2. 只有编译到主程序的符号才能被找到
3. 静态库中的符号默认不导出到主程序符号表

### 解决方案原理：
1. 直接将MELP源文件编译到主程序
2. 使用 `__attribute__((visibility("default")))` 导出符号
3. 通过包装器提供统一的C接口

这种方案比MethodChannel更直接，性能更好，且与Android保持一致。
